Adam 作为安全经理，虽然有多年的从业经历，但是面对上千个司机时，哪些司机是需要激励的好
司机，哪些司机需要安全教育提升，哪些司机需要被淘汰，即将入职的司机，是否符合企业对司机技能、
驾驶⻛格的要求，Adam 很难全面管理，并将个人的多年经验根据不同司机进行专项提升。面对这些问题
Adam 急需一套有效的管理系统帮助他全面提升公司司机的安全意识和驾驶技能。
Ben 作为企业的安全员，不但需要日常对司机⻛险进行及时的干预，还需要通过司机培训，帮助司机
改善驾驶技能和驾驶⻛格。但是过多的安全报警导致大量的人工审核操作，难以持续推行，并且单一的培
训话术导致培训效果不佳，司机的情况并无法用几套话术就能覆盖。培训后司机安全意识是否提升？没有
工具及时跟踪效果。
平台提供安全管理 Agent 帮助⻋企人员工作。
以下是交互介绍
Adam（安全经理） 职责：查看整体情况，发现⻛险，分配任务
画面 1：
输入 or 自动：今天有什么待安排事项？
回复：Carl 完成了入职试用，需要审批 Carl 的驾驶技能、驾驶⻛格是否满足公司入职要求。
画面 2：
输入：Carl 的驾驶行为表现如何？
展示：弹出 Carl 的标签⻚。
回复：Carl 是一个经常跟⻋过近的司机、经常分心。
输入：Carl 不适合  
回复收到，已生成反馈。
画面 3：
输入：公司里面有哪些高⻛险司机
展示：弹出⻛险司机排行榜的⻚面。
输入：有哪些司机是屡教不改的司机
展示：弹出对应⻚面。
输入：针对这些司机生成约谈任务给各个⻋队安全员。
展示：显示任务生成⻚面
回复：任务已经分配完成

画面 4：
输入：⻋队有哪些高⻛险行为
展示：弹出组织行为得分⻚面
用户挑选得分最低司机，查看司机详情。
得分小于 60 分的司机生成培训任务分配各⻋队安全员和司机
画面 5：
司机培训关键路径信息录入
对于专用⻋辆的特殊场景驾驶需要注意的关键点，录入关键内容，输入：XXXXX
回复：对于专用⻋辆的 XX 情况，会重点对司机进行管理。

Ben（安全员） 职责：执行分配任务，关注司机实时⻛险
画面 1：
系统提示：有任务来了。点击后显示任务列表。
画面 2：
点击 David 的培训任务，显示：“David 因为 6 次上榜，需要进行约谈培训”
画面 3：
David 的⻛险行为有哪些
弹出⻛险行为界面，查看对应⻛险行为。给出⻛险情况的说明和证据展示。
旁白：“这是一个经常跟⻋过近、经常激进变道，经常分心的司机”
对 David，生成对应的培训方案
弹出任务⻚面，展示培训方案。
1、⻛险总结
2、⻛险标签，证据，行为描述，改善建议。
约 xxx 于 5 月 10 日下午 4 是在独立大楼会谈室 3 进行面对面培训
已预约会谈室 3
已下发约谈任务给 David。
司机手机 app 收到任务的提示
画面 4：
录入企业专有的培训内容
David（司机）职责：培训学习，行为改善
画面 1；
学习任务提示：经常激进变道学习
点击开始进行学习。
虚拟人界面：证据展示，行为描述，防御性驾驶培训
